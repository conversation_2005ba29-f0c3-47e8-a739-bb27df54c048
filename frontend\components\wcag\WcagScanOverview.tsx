/**
 * WCAG Scan Results Overview Component
 * Displays high-level scan results and metrics
 */

'use client';

import React from 'react';
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  BarChart3,

  Zap,
  Target
} from 'lucide-react';
import { WcagScanResult, RiskLevel, WcagLevel } from '../../types/wcag';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Alert } from '../ui/alert';

interface WcagScanOverviewProps {
  scanResult: WcagScanResult;
}

const WcagScanOverview: React.FC<WcagScanOverviewProps> = ({ scanResult }) => {
  /**
   * Get risk level color
   */
  const getRiskColor = (riskLevel: RiskLevel): 'default' | 'secondary' | 'destructive' | 'outline' => {
    switch (riskLevel) {
      case 'low': return 'secondary';
      case 'medium': return 'outline';
      case 'high': return 'destructive';
      case 'critical': return 'destructive';
      default: return 'default';
    }
  };

  /**
   * Get level achievement color
   */
  const getLevelColor = (level: WcagLevel | 'FAIL'): 'default' | 'secondary' | 'destructive' | 'outline' => {
    switch (level) {
      case 'AAA': return 'secondary';
      case 'AA': return 'secondary';
      case 'A': return 'outline';
      case 'FAIL': return 'destructive';
      default: return 'default';
    }
  };

  /**
   * Format duration
   */
  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  /**
   * Calculate automation percentage
   */
  const automationPercentage = Math.round(scanResult.summary.automationRate * 100);

  return (
    <div className="space-y-6">
      {/* Status Alert */}
      {scanResult.status === 'completed' && (
        <Alert variant={scanResult.riskLevel === 'low' ? 'default' : 'destructive'}>
          <CheckCircle className="h-4 w-4" />
          <div>
            <h4 className="font-semibold">
              Scan Completed - {scanResult.levelAchieved} Level Achieved
            </h4>
            <p className="text-sm">
              Overall Score: {scanResult.overallScore}/100 | Risk Level: {scanResult.riskLevel.toUpperCase()}
            </p>
          </div>
        </Alert>
      )}

      {scanResult.status === 'failed' && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <div>
            <h4 className="font-semibold">Scan Failed</h4>
            <p className="text-sm">
              The WCAG compliance scan could not be completed. Please try again.
            </p>
          </div>
        </Alert>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Overall Score */}
        <Card>
          <CardContent className="p-6 text-center">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 text-primary" />
            <div className="text-3xl font-bold text-primary mb-1">
              {scanResult.overallScore}
            </div>
            <p className="text-sm text-muted-foreground mb-2">Overall Score</p>
            <Progress value={scanResult.overallScore} className="h-2" />
          </CardContent>
        </Card>

        {/* Level Achieved */}
        <Card>
          <CardContent className="p-6 text-center">
            <Target className="h-8 w-8 mx-auto mb-2 text-green-600" />
            <Badge 
              variant={getLevelColor(scanResult.levelAchieved)} 
              className="text-lg px-3 py-1 mb-2"
            >
              {scanResult.levelAchieved}
            </Badge>
            <p className="text-sm text-muted-foreground">WCAG Level Achieved</p>
          </CardContent>
        </Card>

        {/* Risk Level */}
        <Card>
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-yellow-600" />
            <Badge 
              variant={getRiskColor(scanResult.riskLevel)} 
              className="text-lg px-3 py-1 mb-2"
            >
              {scanResult.riskLevel.toUpperCase()}
            </Badge>
            <p className="text-sm text-muted-foreground">Risk Level</p>
          </CardContent>
        </Card>

        {/* Automation Rate */}
        <Card>
          <CardContent className="p-6 text-center">
            <Zap className="h-8 w-8 mx-auto mb-2 text-blue-600" />
            <div className="text-3xl font-bold text-blue-600 mb-1">
              {automationPercentage}%
            </div>
            <p className="text-sm text-muted-foreground">Automated Analysis</p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Check Results Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Check Results Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Passed Checks</span>
                <span className="text-sm font-medium text-green-600">
                  {scanResult.summary.passedAutomatedChecks}
                </span>
              </div>
              <Progress
                value={(scanResult.summary.passedAutomatedChecks / scanResult.summary.totalAutomatedChecks) * 100}
                className="h-2"
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Failed Checks</span>
                <span className="text-sm font-medium text-red-600">
                  {scanResult.summary.failedAutomatedChecks}
                </span>
              </div>
              <Progress
                value={(scanResult.summary.failedAutomatedChecks / scanResult.summary.totalAutomatedChecks) * 100}
                className="h-2"
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Manual Review Items</span>
                <span className="text-sm font-medium text-blue-600">
                  {scanResult.summary.manualReviewItems}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">
                Requires manual review
              </p>
            </div>

            <div className="pt-2 border-t">
              <p className="text-sm text-muted-foreground">
                Total Automated Checks: {scanResult.summary.totalAutomatedChecks}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Category Scores */}
        <Card>
          <CardHeader>
            <CardTitle>Category Scores</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(scanResult.summary.categoryScores).map(([category, score]) => {
              const numericScore = Number(score);
              return (
                <div key={category} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm capitalize">{category}</span>
                    <span className="text-sm font-medium">{numericScore}/100</span>
                  </div>
                  <Progress
                    value={numericScore}
                    className={`h-2 ${
                      numericScore >= 80 ? 'text-green-600' :
                      numericScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                    }`}
                  />
                </div>
              );
            })}
          </CardContent>
        </Card>
      </div>

      {/* Scan Metadata */}
      <Card>
        <CardHeader>
          <CardTitle>Scan Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Target URL</p>
              <p className="text-sm font-medium break-all">{scanResult.targetUrl}</p>
            </div>

            <div>
              <p className="text-sm text-muted-foreground">Scan Duration</p>
              <p className="text-sm font-medium">
                {scanResult.metadata.duration ? formatDuration(scanResult.metadata.duration) : 'N/A'}
              </p>
            </div>

            <div>
              <p className="text-sm text-muted-foreground">Scan Date</p>
              <p className="text-sm font-medium">
                {new Date(scanResult.metadata.startTime).toLocaleDateString()}
              </p>
            </div>

            <div>
              <p className="text-sm text-muted-foreground">Scan ID</p>
              <p className="text-xs font-mono bg-muted px-2 py-1 rounded">
                {scanResult.scanId}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WcagScanOverview;
